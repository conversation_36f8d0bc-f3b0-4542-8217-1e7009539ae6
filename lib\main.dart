import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

// Main function to run the Flutter application
void main() {
  runApp(const PetGroomingApp());
}

// The root widget of the application
class PetGroomingApp extends StatelessWidget {
  const PetGroomingApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Glowing Pet',
      debugShowCheckedModeBanner: false,
      // Define the overall theme for the application
      theme: ThemeData(
        primarySwatch: Colors.blue,
        // Use a clean, modern font
        fontFamily: 'Poppins',
        scaffoldBackgroundColor: const Color(0xFFF8F9FA),
        // Define color scheme for a fresh and friendly look
        colorScheme: const ColorScheme.light(
          primary: Color(0xFF007BFF),
          secondary: Color(0xFF00C6AE),
          background: Color(0xFFF8F9FA),
          surface: Colors.white,
          onPrimary: Colors.white,
          onSecondary: Colors.black,
          onBackground: Color(0xFF343A40),
          onSurface: Color(0xFF343A40),
        ),
        // Define text theme for consistent typography
        textTheme: const TextTheme(
          displayLarge: TextStyle(fontSize: 32.0, fontWeight: FontWeight.bold, color: Color(0xFF343A40)),
          headlineMedium: TextStyle(fontSize: 24.0, fontWeight: FontWeight.w600, color: Color(0xFF343A40)),
          bodyLarge: TextStyle(fontSize: 16.0, color: Color(0xFF495057)),
          bodyMedium: TextStyle(fontSize: 14.0, color: Color(0xFF6C757D)),
        ),
        // Define button theme for consistent button styles
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF007BFF),
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          ),
        ),
      ),
      // Start the app with the MainScreen widget
      home: const MainScreen(),
    );
  }
}

// Main screen widget that holds the bottom navigation bar and pages
class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _selectedIndex = 0;

  // List of pages to be displayed by the bottom navigation bar
  static const List<Widget> _widgetOptions = <Widget>[
    HomePage(),
    ServicesPage(),
    ProfilePage(),
  ];

  // Function to handle tab selection
  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: _widgetOptions.elementAt(_selectedIndex),
      ),
      // Bottom navigation bar for easy page switching
      bottomNavigationBar: BottomNavigationBar(
        items: const <BottomNavigationBarItem>[
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.cut),
            label: 'Services',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
        currentIndex: _selectedIndex,
        selectedItemColor: Theme.of(context).colorScheme.primary,
        onTap: _onItemTapped,
      ),
    );
  }
}

// The Home Page of the application
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // A custom app bar that expands and collapses
          SliverAppBar(
            expandedHeight: 250.0,
            floating: false,
            pinned: true,
            backgroundColor: Theme.of(context).colorScheme.primary,
            flexibleSpace: FlexibleSpaceBar(
              title: const Text('Paws & Bubbles', style: TextStyle(color: Colors.white)),
              background: Stack(
                fit: StackFit.expand,
                children: [
                  Image.network(
                    'https://images.unsplash.com/photo-1543466835-00a7907e9de1?q=80&w=2874&auto=format&fit=crop',
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(color: Colors.grey[300]),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.black.withOpacity(0.6), Colors.transparent],
                        begin: Alignment.bottomCenter,
                        end: Alignment.topCenter,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Main content of the home page
          SliverList(
            delegate: SliverChildListDelegate(
              [
                const SizedBox(height: 24),
                // Welcome message section
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Welcome, Pet Lover!',
                        style: Theme.of(context).textTheme.headlineMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Book a grooming session for your furry friend.',
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
                // Call to action button to book an appointment
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.calendar_today),
                    label: const Text('Book an Appointment'),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) => const BookingPage()),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 32),
                // Featured services section
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.0),
                  child: Text('Our Services', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  height: 140,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.only(left: 16),
                    children: const [
                      ServiceCard(
                        icon: Icons.shower,
                        title: 'Bath & Brush',
                        color: Colors.lightBlue,
                      ),
                      ServiceCard(
                        icon: Icons.content_cut,
                        title: 'Haircut',
                        color: Colors.orangeAccent,
                      ),
                      ServiceCard(
                        icon: Icons.pets,
                        title: 'Nail Trim',
                        color: Colors.greenAccent,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 32),
                // Happy pets gallery section
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.0),
                  child: Text('Happy Clients', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  height: 200,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.only(left: 16),
                    children: [
                      _buildGalleryImage('https://images.unsplash.com/photo-1598875706253-33fa34527562?q=80&w=2952&auto=format&fit=crop'),
                      _buildGalleryImage('https://images.unsplash.com/photo-1552053831-71594a27632d?q=80&w=2824&auto=format&fit=crop'),
                      _buildGalleryImage('https://images.unsplash.com/photo-1537151608828-ea2b11777ee8?q=80&w=2884&auto=format&fit=crop'),
                    ],
                  ),
                ),
                const SizedBox(height: 32),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper widget to build gallery images
  Widget _buildGalleryImage(String url) {
    return Padding(
      padding: const EdgeInsets.only(right: 16.0),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Image.network(
          url,
          width: 150,
          height: 200,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) => Container(
            width: 150,
            height: 200,
            color: Colors.grey[200],
            child: const Icon(Icons.pets, color: Colors.grey, size: 50),
          ),
        ),
      ),
    );
  }
}

// A reusable card widget for displaying services
class ServiceCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final Color color;

  const ServiceCard({
    super.key,
    required this.icon,
    required this.title,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 120,
      margin: const EdgeInsets.only(right: 16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 40, color: color),
          const SizedBox(height: 12),
          Text(title, textAlign: TextAlign.center, style: const TextStyle(fontWeight: FontWeight.w600)),
        ],
      ),
    );
  }
}

// The Services Page, listing all available services
class ServicesPage extends StatelessWidget {
  const ServicesPage({super.key});

  // Mock data for services
  final List<Map<String, dynamic>> services = const [
    {'name': 'Bath & Brush', 'price': 50.00, 'description': 'A refreshing bath with premium shampoo and a thorough brushing.'},
    {'name': 'Full Haircut', 'price': 75.00, 'description': 'A stylish haircut tailored to your pet\'s breed and your preference.'},
    {'name': 'Nail Trimming', 'price': 20.00, 'description': 'Careful and gentle nail trimming and filing.'},
    {'name': 'De-Shedding Treatment', 'price': 65.00, 'description': 'Reduces shedding by removing undercoat and loose hair.'},
    {'name': 'Teeth Brushing', 'price': 15.00, 'description': 'Helps maintain dental hygiene and fresh breath.'},
    {'name': 'The Spa Special', 'price': 120.00, 'description': 'Includes a bath, haircut, nail trim, and a relaxing pet massage.'},
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Our Services'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 1,
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: services.length,
        itemBuilder: (context, index) {
          final service = services[index];
          return Card(
            margin: const EdgeInsets.only(bottom: 16),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            elevation: 2,
            child: ListTile(
              contentPadding: const EdgeInsets.all(16),
              title: Text(service['name'], style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontSize: 18)),
              subtitle: Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(service['description']),
              ),
              trailing: Text(
                '\$${service['price'].toStringAsFixed(2)}',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

// The Booking Page with a form to schedule an appointment
class BookingPage extends StatefulWidget {
  const BookingPage({super.key});

  @override
  State<BookingPage> createState() => _BookingPageState();
}

class _BookingPageState extends State<BookingPage> {
  final _formKey = GlobalKey<FormState>();
  String? _selectedService;
  DateTime? _selectedDate;
  TimeOfDay? _selectedTime;

  final List<String> _services = ['Bath & Brush', 'Full Haircut', 'Nail Trimming', 'De-Shedding Treatment'];

  // Function to show the date picker
  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  // Function to show the time picker
  Future<void> _selectTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );
    if (picked != null && picked != _selectedTime) {
      setState(() {
        _selectedTime = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Book Appointment'),
        elevation: 1,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              // Form fields for booking details
              _buildTextField(label: 'Your Name', icon: Icons.person_outline),
              const SizedBox(height: 16),
              _buildTextField(label: 'Pet\'s Name', icon: Icons.pets_outlined),
              const SizedBox(height: 16),
              _buildDropdownField(),
              const SizedBox(height: 16),
              _buildDateTimePicker(),
              const SizedBox(height: 32),
              // Submit button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      // Show a confirmation dialog
                      showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return AlertDialog(
                            title: const Text('Booking Confirmed!'),
                            content: const Text('Your appointment has been successfully scheduled.'),
                            actions: <Widget>[
                              TextButton(
                                child: const Text('OK'),
                                onPressed: () {
                                  Navigator.of(context).pop(); // Close dialog
                                  Navigator.of(context).pop(); // Go back from booking page
                                },
                              ),
                            ],
                          );
                        },
                      );
                    }
                  },
                  child: const Text('Confirm Booking'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper widget for text form fields
  Widget _buildTextField({required String label, required IconData icon}) {
    return TextFormField(
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter $label';
        }
        return null;
      },
    );
  }

  // Helper widget for the service dropdown
  Widget _buildDropdownField() {
    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        labelText: 'Service',
        prefixIcon: const Icon(Icons.cut_outlined),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      value: _selectedService,
      hint: const Text('Select a service'),
      onChanged: (String? newValue) {
        setState(() {
          _selectedService = newValue;
        });
      },
      items: _services.map<DropdownMenuItem<String>>((String value) {
        return DropdownMenuItem<String>(
          value: value,
          child: Text(value),
        );
      }).toList(),
      validator: (value) => value == null ? 'Please select a service' : null,
    );
  }

  // Helper widget for date and time pickers
  Widget _buildDateTimePicker() {
    return Row(
      children: [
        Expanded(
          child: InkWell(
            onTap: () => _selectDate(context),
            child: InputDecorator(
              decoration: InputDecoration(
                labelText: 'Date',
                prefixIcon: const Icon(Icons.calendar_today_outlined),
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
              ),
              child: Text(
                _selectedDate == null
                    ? 'Select Date'
                    : DateFormat.yMMMd().format(_selectedDate!),
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: InkWell(
            onTap: () => _selectTime(context),
            child: InputDecorator(
              decoration: InputDecoration(
                labelText: 'Time',
                prefixIcon: const Icon(Icons.access_time_outlined),
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
              ),
              child: Text(
                _selectedTime == null
                    ? 'Select Time'
                    : _selectedTime!.format(context),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

// The Profile Page to display user information
class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Profile'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 1,
      ),
      body: ListView(
        padding: const EdgeInsets.all(24.0),
        children: [
          // Profile header with avatar and name
          Column(
            children: [
              const CircleAvatar(
                radius: 50,
                backgroundImage: NetworkImage('https://i.pravatar.cc/150?u=a042581f4e29026704d'),
              ),
              const SizedBox(height: 16),
              Text('Alex Doe', style: Theme.of(context).textTheme.headlineMedium),
              const SizedBox(height: 4),
              Text('<EMAIL>', style: Theme.of(context).textTheme.bodyMedium),
            ],
          ),
          const SizedBox(height: 32),
          const Divider(),
          // Profile options list
          _buildProfileOption(context, icon: Icons.history, title: 'Booking History'),
          _buildProfileOption(context, icon: Icons.pets, title: 'My Pets'),
          _buildProfileOption(context, icon: Icons.payment, title: 'Payment Methods'),
          _buildProfileOption(context, icon: Icons.settings, title: 'Settings'),
          const Divider(),
          _buildProfileOption(context, icon: Icons.logout, title: 'Log Out', color: Colors.red),
        ],
      ),
    );
  }

  // Helper widget for creating profile list items
  Widget _buildProfileOption(BuildContext context, {required IconData icon, required String title, Color? color}) {
    return ListTile(
      leading: Icon(icon, color: color ?? Theme.of(context).colorScheme.primary),
      title: Text(title, style: TextStyle(color: color)),
      trailing: const Icon(Icons.chevron_right),
      onTap: () {
        // Placeholder for navigation or action
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('$title tapped!')),
        );
      },
    );
  }
}
